apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app.kubernetes.io/component: proxy
    app.kubernetes.io/managed-by: envoy-gateway
    app.kubernetes.io/name: envoy
    gateway.envoyproxy.io/owning-gateway-name: gateway-1
    gateway.envoyproxy.io/owning-gateway-namespace: ns1
    gateway.networking.k8s.io/gateway-name: gateway-1
  name: gateway-1
  namespace: ns1
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    name: gateway-1
    uid: test-owner-reference-uid-for-gateway
spec:
  progressDeadlineSeconds: 600
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/component: proxy
      app.kubernetes.io/managed-by: envoy-gateway
      app.kubernetes.io/name: envoy
      gateway.envoyproxy.io/owning-gateway-name: gateway-1
      gateway.envoyproxy.io/owning-gateway-namespace: ns1
      gateway.networking.k8s.io/gateway-name: gateway-1
  strategy:
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/path: /stats/prometheus
        prometheus.io/port: "19001"
        prometheus.io/scrape: "true"
      creationTimestamp: null
      labels:
        app.kubernetes.io/component: proxy
        app.kubernetes.io/managed-by: envoy-gateway
        app.kubernetes.io/name: envoy
        gateway.envoyproxy.io/owning-gateway-name: gateway-1
        gateway.envoyproxy.io/owning-gateway-namespace: ns1
        gateway.networking.k8s.io/gateway-name: gateway-1
    spec:
      automountServiceAccountToken: false
      containers:
      - args:
        - --service-cluster ns1/gateway-1
        - --service-node $(ENVOY_POD_NAME)
        - |
          --config-yaml admin:
            access_log:
            - name: envoy.access_loggers.file
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                path: /dev/null
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 19000
          cluster_manager:
            local_cluster_name: local_cluster
          node:
            locality:
              zone: $(ENVOY_SERVICE_ZONE)
          layered_runtime:
            layers:
            - name: global_config
              static_layer:
                envoy.restart_features.use_eds_cache_for_ads: true
                re2.max_program_size.error_level: **********
                re2.max_program_size.warn_level: 1000
          dynamic_resources:
            ads_config:
              api_type: DELTA_GRPC
              transport_api_version: V3
              grpc_services:
              - envoy_grpc:
                  cluster_name: xds_cluster
              set_node_on_first_message_only: true
            lds_config:
              ads: {}
              resource_api_version: V3
            cds_config:
              ads: {}
              resource_api_version: V3
          static_resources:
            listeners:
            - name: envoy-gateway-proxy-stats-0.0.0.0-19001
              address:
                socket_address:
                  address: '0.0.0.0'
                  port_value: 19001
                  protocol: TCP
              bypass_overload_manager: true
              filter_chains:
              - filters:
                - name: envoy.filters.network.http_connection_manager
                  typed_config:
                    "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                    stat_prefix: eg-stats-http
                    normalize_path: true
                    route_config:
                      name: local_route
                      virtual_hosts:
                      - name: prometheus_stats
                        domains:
                        - "*"
                        routes:
                        - match:
                            path: /stats/prometheus
                            headers:
                            - name: ":method"
                              string_match:
                                exact: GET
                          route:
                            cluster: prometheus_stats
                    http_filters:
                    - name: envoy.filters.http.router
                      typed_config:
                        "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
            clusters:
            - name: prometheus_stats
              connect_timeout: 0.250s
              type: STATIC
              lb_policy: ROUND_ROBIN
              load_assignment:
                cluster_name: prometheus_stats
                endpoints:
                - lb_endpoints:
                  - endpoint:
                      address:
                        socket_address:
                          address: 127.0.0.1
                          port_value: 19000
            - connect_timeout: 10s
              lb_policy: ROUND_ROBIN
              load_assignment:
                cluster_name: local_cluster
                endpoints:
                - lb_endpoints:
                  - endpoint:
                      address:
                        socket_address:
                          address: 127.0.0.1
                          port_value: 10080
                    load_balancing_weight: 1
                  load_balancing_weight: 1
                  locality:
                    zone: $(ENVOY_SERVICE_ZONE)
              name: local_cluster
              type: STATIC
            - connect_timeout: 10s
              load_assignment:
                cluster_name: xds_cluster
                endpoints:
                - load_balancing_weight: 1
                  lb_endpoints:
                  - load_balancing_weight: 1
                    endpoint:
                      address:
                        socket_address:
                          address: envoy-gateway.envoy-gateway-system.svc.cluster.local
                          port_value: 18000
              typed_extension_protocol_options:
                envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
                  "@type": "type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions"
                  explicit_http_config:
                    http2_protocol_options:
                      connection_keepalive:
                        interval: 30s
                        timeout: 5s
                  http_filters:
                  - name: envoy.filters.http.credential_injector
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.credential_injector.v3.CredentialInjector
                      credential:
                        name: envoy.http.injected_credentials.generic
                        typed_config:
                          "@type": type.googleapis.com/envoy.extensions.http.injected_credentials.generic.v3.Generic
                          credential:
                            name: jwt-sa-bearer
                      overwrite: true
                  - name: envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
              name: xds_cluster
              type: STRICT_DNS
              transport_socket:
                name: envoy.transport_sockets.tls
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
                  common_tls_context:
                    tls_params:
                      tls_maximum_protocol_version: TLSv1_3
                    validation_context_sds_secret_config:
                      name: xds_trusted_ca
                      sds_config:
                        path_config_source:
                          path: /sds/xds-trusted-ca.json
                        resource_api_version: V3
            secrets:
            - name: jwt-sa-bearer
              generic_secret:
                secret:
                  filename: "/var/run/secrets/token/sa-token"
          overload_manager:
            refresh_interval: 0.25s
            resource_monitors:
            - name: "envoy.resource_monitors.global_downstream_max_connections"
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.resource_monitors.downstream_connections.v3.DownstreamConnectionsConfig
                max_active_downstream_connections: 50000
        - --log-level warn
        - --cpuset-threads
        - --drain-strategy immediate
        - --component-log-level misc:error
        - --drain-time-s 60
        command:
        - envoy
        env:
        - name: ENVOY_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: ENVOY_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: ENVOY_SERVICE_ZONE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.annotations['topology.kubernetes.io/zone']
        image: docker.io/envoyproxy/envoy:distroless-dev
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            httpGet:
              path: /shutdown/ready
              port: 19002
              scheme: HTTP
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /ready
            port: 19003
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: envoy
        ports:
        - containerPort: 19001
          name: metrics
          protocol: TCP
        - containerPort: 19003
          name: readiness
          protocol: TCP
        readinessProbe:
          failureThreshold: 1
          httpGet:
            path: /ready
            port: 19003
            scheme: HTTP
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          requests:
            cpu: 100m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          privileged: false
          runAsGroup: 65532
          runAsNonRoot: true
          runAsUser: 65532
          seccompProfile:
            type: RuntimeDefault
        startupProbe:
          failureThreshold: 30
          httpGet:
            path: /ready
            port: 19003
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /certs
          name: certs
          readOnly: true
        - mountPath: /sds
          name: sds
        - mountPath: /var/run/secrets/token
          name: sa-token
          readOnly: true
      - args:
        - envoy
        - shutdown-manager
        command:
        - envoy-gateway
        env:
        - name: ENVOY_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: ENVOY_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: ENVOY_SERVICE_ZONE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.annotations['topology.kubernetes.io/zone']
        image: docker.io/envoyproxy/gateway-dev:latest
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command:
              - envoy-gateway
              - envoy
              - shutdown
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 19002
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: shutdown-manager
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 19002
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          requests:
            cpu: 10m
            memory: 32Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          privileged: false
          runAsGroup: 65532
          runAsNonRoot: true
          runAsUser: 65532
          seccompProfile:
            type: RuntimeDefault
        startupProbe:
          failureThreshold: 30
          httpGet:
            path: /healthz
            port: 19002
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccountName: custom-sa
      terminationGracePeriodSeconds: 360
      volumes:
      - name: sa-token
        projected:
          defaultMode: 420
          sources:
          - serviceAccountToken:
              audience: envoy-gateway.envoy-gateway-system.svc.cluster.local
              expirationSeconds: 3600
              path: sa-token
      - configMap:
          defaultMode: 420
          items:
          - key: ca.crt
            path: ca.crt
          name: gateway-1
          optional: false
        name: certs
      - configMap:
          defaultMode: 420
          items:
          - key: xds-trusted-ca.json
            path: xds-trusted-ca.json
          name: gateway-1
          optional: false
        name: sds
status: {}
